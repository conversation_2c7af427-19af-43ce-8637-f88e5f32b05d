import React from 'react';
import { Card, Table, Button, Space, Tag, DatePicker } from 'antd';
import { SearchOutlined, EyeOutlined, EditOutlined } from '@ant-design/icons';

const OrderManage = () => {
  const columns = [
    {
      title: '订单号',
      dataIndex: 'orderNo',
      key: 'orderNo',
    },
    {
      title: '客户姓名',
      dataIndex: 'customerName',
      key: 'customerName',
    },
    {
      title: '休息室',
      dataIndex: 'lounge',
      key: 'lounge',
    },
    {
      title: '预订时间',
      dataIndex: 'bookingTime',
      key: 'bookingTime',
    },
    {
      title: '使用时长',
      dataIndex: 'duration',
      key: 'duration',
    },
    {
      title: '金额',
      dataIndex: 'amount',
      key: 'amount',
      render: (amount) => `¥${amount}`,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status) => {
        const colorMap = {
          '已预订': 'blue',
          '使用中': 'green',
          '已完成': 'default',
          '已取消': 'red',
        };
        return <Tag color={colorMap[status]}>{status}</Tag>;
      },
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="middle">
          <Button type="link" icon={<EyeOutlined />}>
            查看
          </Button>
          <Button type="link" icon={<EditOutlined />}>
            编辑
          </Button>
        </Space>
      ),
    },
  ];

  const data = [
    {
      key: '1',
      orderNo: 'ORD20241201001',
      customerName: '张三',
      lounge: 'VIP休息室A',
      bookingTime: '2024-12-01 14:30',
      duration: '2小时',
      amount: 299,
      status: '使用中',
    },
    {
      key: '2',
      orderNo: 'ORD20241201002',
      customerName: '李四',
      lounge: 'VIP休息室B',
      bookingTime: '2024-12-01 16:00',
      duration: '3小时',
      amount: 399,
      status: '已预订',
    },
  ];

  return (
    <div style={{ padding: 24 }}>
      <Card
        title="订单管理"
        extra={
          <Space>
            <DatePicker.RangePicker />
            <Button type="primary" icon={<SearchOutlined />}>
              搜索
            </Button>
          </Space>
        }
      >
        <Table columns={columns} dataSource={data} />
      </Card>
    </div>
  );
};

export default OrderManage;
