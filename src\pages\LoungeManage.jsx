import React from 'react';
import { Card, Table, Button, Space, Tag } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined } from '@ant-design/icons';

const LoungeManage = () => {
  const columns = [
    {
      title: '休息室名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '位置',
      dataIndex: 'location',
      key: 'location',
    },
    {
      title: '容量',
      dataIndex: 'capacity',
      key: 'capacity',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status) => (
        <Tag color={status === '营业中' ? 'green' : 'red'}>
          {status}
        </Tag>
      ),
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="middle">
          <Button type="link" icon={<EditOutlined />}>
            编辑
          </Button>
          <Button type="link" danger icon={<DeleteOutlined />}>
            删除
          </Button>
        </Space>
      ),
    },
  ];

  const data = [
    {
      key: '1',
      name: 'VIP休息室A',
      location: 'T1航站楼2层',
      capacity: 50,
      status: '营业中',
    },
    {
      key: '2',
      name: 'VIP休息室B',
      location: 'T2航站楼3层',
      capacity: 80,
      status: '营业中',
    },
  ];

  return (
    <div style={{ padding: 24 }}>
      <Card
        title="休息室管理"
        extra={
          <Button type="primary" icon={<PlusOutlined />}>
            新增休息室
          </Button>
        }
      >
        <Table columns={columns} dataSource={data} />
      </Card>
    </div>
  );
};

export default LoungeManage;
