import React from 'react';
import { createBrowserRouter, createHashRouter } from 'react-router-dom';
import Home from '@/pages/Home';
import LoungeManage from '@/pages/LoungeManage';
import OrderManage from '@/pages/OrderManage';
import CustomerManage from '@/pages/CustomerManage';
import ProductManage from '@/pages/ProductManage';
import Statistics from '@/pages/Statistics';
import Settings from '@/pages/Settings';
import Layout from '@/components/Layout';

// 根据环境变量设置路由模式
const routeMode = import.meta.env.VITE_ROUTE_MODE || 'hash';

const routes = [
  {
    path: '/',
    element: <Layout />,
    children: [
      { path: '/', element: <Home /> },
      { path: '/lounge', element: <LoungeManage /> },
      { path: '/orders', element: <OrderManage /> },
      { path: '/customers', element: <CustomerManage /> },
      { path: '/products', element: <ProductManage /> },
      { path: '/statistics', element: <Statistics /> },
      { path: '/settings', element: <Settings /> }
    ]
  }
];

const router = routeMode === 'history'
  ? createBrowserRouter(routes)
  : createHashRouter(routes);

export default router;