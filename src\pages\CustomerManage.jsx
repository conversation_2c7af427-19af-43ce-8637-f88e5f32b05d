import React from 'react';
import { Card, Table, Button, Space, Tag, Input } from 'antd';
import { SearchOutlined, PlusOutlined, EditOutlined, UserOutlined } from '@ant-design/icons';

const CustomerManage = () => {
  const columns = [
    {
      title: '客户姓名',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '手机号',
      dataIndex: 'phone',
      key: 'phone',
    },
    {
      title: '邮箱',
      dataIndex: 'email',
      key: 'email',
    },
    {
      title: '会员等级',
      dataIndex: 'memberLevel',
      key: 'memberLevel',
      render: (level) => {
        const colorMap = {
          '普通会员': 'default',
          '银卡会员': 'blue',
          '金卡会员': 'gold',
          '钻石会员': 'purple',
        };
        return <Tag color={colorMap[level]}>{level}</Tag>;
      },
    },
    {
      title: '累计消费',
      dataIndex: 'totalSpent',
      key: 'totalSpent',
      render: (amount) => `¥${amount}`,
    },
    {
      title: '注册时间',
      dataIndex: 'registerTime',
      key: 'registerTime',
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="middle">
          <Button type="link" icon={<UserOutlined />}>
            详情
          </Button>
          <Button type="link" icon={<EditOutlined />}>
            编辑
          </Button>
        </Space>
      ),
    },
  ];

  const data = [
    {
      key: '1',
      name: '张三',
      phone: '13800138000',
      email: '<EMAIL>',
      memberLevel: '金卡会员',
      totalSpent: 2580,
      registerTime: '2024-01-15',
    },
    {
      key: '2',
      name: '李四',
      phone: '13900139000',
      email: '<EMAIL>',
      memberLevel: '银卡会员',
      totalSpent: 1200,
      registerTime: '2024-03-20',
    },
  ];

  return (
    <div style={{ padding: 24 }}>
      <Card
        title="客户管理"
        extra={
          <Space>
            <Input.Search
              placeholder="搜索客户姓名或手机号"
              style={{ width: 250 }}
              enterButton={<SearchOutlined />}
            />
            <Button type="primary" icon={<PlusOutlined />}>
              新增客户
            </Button>
          </Space>
        }
      >
        <Table columns={columns} dataSource={data} />
      </Card>
    </div>
  );
};

export default CustomerManage;
