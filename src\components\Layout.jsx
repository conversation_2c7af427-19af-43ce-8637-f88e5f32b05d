import React from 'react';
import { ProLayout } from '@ant-design/pro-layout';
import { useNavigate, useLocation, Outlet } from 'react-router-dom';
import {
  DashboardOutlined,
  ShopOutlined,
  ShoppingCartOutlined,
  UserOutlined,
  AppstoreOutlined,
  BarChartOutlined,
  SettingOutlined
} from '@ant-design/icons';

const Layout = () => {
  const navigate = useNavigate();
  const location = useLocation();

  const menuData = [
    {
      path: '/',
      name: '仪表板',
      icon: <DashboardOutlined />,
      key: 'dashboard'
    },
    {
      path: '/lounge',
      name: '休息室管理',
      icon: <ShopOutlined />,
      key: 'lounge'
    },
    {
      path: '/orders',
      name: '订单管理',
      icon: <ShoppingCartOutlined />,
      key: 'orders'
    },
    {
      path: '/customers',
      name: '客户管理',
      icon: <UserOutlined />,
      key: 'customers'
    },
    {
      path: '/products',
      name: '产品管理',
      icon: <AppstoreOutlined />,
      key: 'products'
    },
    {
      path: '/statistics',
      name: '统计报表',
      icon: <BarChartOutlined />,
      key: 'statistics'
    },
    {
      path: '/settings',
      name: '系统设置',
      icon: <SettingOutlined />,
      key: 'settings'
    }
  ];

  return (
    <ProLayout
      title="高舱休息室销售系统"
      logo="https://gw.alipayobjects.com/zos/antfincdn/PmY%24TNNDBI/logo.svg"
      route={{
        routes: menuData,
      }}
      location={{
        pathname: location.pathname,
      }}
      menuItemRender={(item, dom) => (
        <div onClick={() => navigate(item.path || '/')}>
          {dom}
        </div>
      )}
      siderWidth={250}
      layout="side"
      navTheme="light"
      headerTheme="light"
      fixSiderbar
      footerRender={() => (
        <div style={{
          padding: '1rem',
          textAlign: 'center',
          backgroundColor: '#f0f0f0',
          marginTop: '2rem',
          borderTop: '1px solid #e8e8e8'
        }}>
          高舱休息室销售系统 © 2024
        </div>
      )}
    >
      <Outlet />
    </ProLayout>
  );
};

export default Layout;